<!-- Modern Campaign Show Page -->
<div class="bg-gray-100 min-h-screen">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Enhanced Header Section -->
    <div class="bg-white rounded-xl shadow-sm p-8 mb-8">
      <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between">
        <div class="flex-1">
          <!-- Campaign Title and Status -->
          <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 mb-6">
            <div class="flex items-center space-x-3 mb-3 sm:mb-0">
              <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-sm">
                <%= render 'shared/icons/heroicon', name: 'chart-bar', variant: 'solid', css_class: 'w-8 h-8 text-white' %>
              </div>
              <h1 class="text-3xl font-bold text-gray-900"><%= @campaign.name %></h1>
            </div>
            <%= render 'dashboard/status_badge',
                status: @campaign.status,
                text: @campaign.status.titleize,
                variant: 'soft',
                size: 'md' %>
          </div>

          <!-- Campaign Description -->
          <% if @campaign.description.present? %>
            <p class="text-gray-600 text-lg mb-6 leading-relaxed"><%= @campaign.description %></p>
          <% end %>

          <!-- Campaign Meta Information -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="flex items-center space-x-3 bg-gray-50 rounded-lg p-4">
              <%= render 'shared/icons/heroicon', name: 'tag', css_class: 'w-5 h-5 text-blue-500' %>
              <div>
                <p class="text-sm text-gray-500">Campaign Type</p>
                <p class="font-semibold text-gray-900"><%= @campaign.campaign_type.titleize %></p>
              </div>
            </div>

            <div class="flex items-center space-x-3 bg-gray-50 rounded-lg p-4">
              <%= render 'shared/icons/heroicon', name: 'currency-dollar', css_class: 'w-5 h-5 text-green-500' %>
              <div>
                <p class="text-sm text-gray-500">Budget</p>
                <p class="font-semibold text-gray-900">$<%= number_with_delimiter(@campaign.budget_in_dollars.round) %></p>
              </div>
            </div>

            <% if @campaign.start_date %>
              <div class="flex items-center space-x-3 bg-gray-50 rounded-lg p-4">
                <%= render 'shared/icons/heroicon', name: 'calendar', css_class: 'w-5 h-5 text-purple-500' %>
                <div>
                  <p class="text-sm text-gray-500">Duration</p>
                  <p class="font-semibold text-gray-900">
                    <%= @campaign.start_date.strftime("%b %d") %>
                    <% if @campaign.end_date %>
                      - <%= @campaign.end_date.strftime("%b %d") %>
                    <% end %>
                  </p>
                </div>
              </div>
            <% end %>

            <div class="flex items-center space-x-3 bg-gray-50 rounded-lg p-4">
              <%= render 'shared/icons/heroicon', name: 'users', css_class: 'w-5 h-5 text-orange-500' %>
              <div>
                <p class="text-sm text-gray-500">Target Audience</p>
                <p class="font-semibold text-gray-900"><%= truncate(@campaign.target_audience, length: 20) %></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-6 lg:mt-0 flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
          <%= link_to edit_campaign_path(@campaign),
              class: "inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md" do %>
            <%= render 'shared/icons/heroicon', name: 'pencil', css_class: 'w-5 h-5 mr-2' %>
            Edit Campaign
          <% end %>

          <%= link_to campaigns_path,
              class: "inline-flex items-center justify-center px-6 py-3 bg-white text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md" do %>
            <%= render 'shared/icons/heroicon', name: 'arrow-left', css_class: 'w-5 h-5 mr-2' %>
            Back to Campaigns
          <% end %>
        </div>
      </div>
    </div>

    <!-- Key Metrics Dashboard -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
      <!-- Performance Metrics -->
      <% if @performance_summary.present? && @performance_summary.any? %>
        <%= render 'dashboard/metric_card',
            title: 'Total Impressions',
            value: number_with_delimiter(@performance_summary[:total_impressions] || 0),
            icon: 'eye',
            color: 'blue',
            trend: nil %>

        <%= render 'dashboard/metric_card',
            title: 'Total Clicks',
            value: number_with_delimiter(@performance_summary[:total_clicks] || 0),
            icon: 'cursor-arrow-rays',
            color: 'green',
            trend: nil %>

        <%= render 'dashboard/metric_card',
            title: 'Conversions',
            value: number_with_delimiter(@performance_summary[:total_conversions] || 0),
            icon: 'chart-bar-square',
            color: 'purple',
            trend: nil %>

        <%= render 'dashboard/metric_card',
            title: 'Revenue',
            value: "$#{number_with_precision(@performance_summary[:total_revenue] || 0, precision: 0, delimiter: ',')}",
            icon: 'currency-dollar',
            color: 'emerald',
            trend: nil %>
      <% else %>
        <%= render 'dashboard/metric_card',
            title: 'Impressions',
            value: '0',
            icon: 'eye',
            color: 'blue',
            trend: nil %>

        <%= render 'dashboard/metric_card',
            title: 'Clicks',
            value: '0',
            icon: 'cursor-arrow-rays',
            color: 'green',
            trend: nil %>

        <%= render 'dashboard/metric_card',
            title: 'Conversions',
            value: '0',
            icon: 'chart-bar-square',
            color: 'purple',
            trend: nil %>

        <%= render 'dashboard/metric_card',
            title: 'Revenue',
            value: '$0',
            icon: 'currency-dollar',
            color: 'emerald',
            trend: nil %>
      <% end %>
    </div>

    <!-- Campaign Progress Section -->
    <% if @campaign.start_date && @campaign.end_date %>
      <div class="bg-white rounded-xl shadow-sm p-8 mb-8">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <%= render 'shared/icons/heroicon', name: 'clock', css_class: 'w-6 h-6 text-blue-500' %>
            <h2 class="text-xl font-semibold text-gray-900">Campaign Progress</h2>
          </div>
          <div class="text-right">
            <div class="text-3xl font-bold text-blue-600"><%= @campaign.progress_percentage %>%</div>
            <div class="text-sm text-gray-500">Complete</div>
          </div>
        </div>

        <%= render 'dashboard/progress_bar',
            value: @campaign.progress_percentage,
            max_value: 100,
            color: 'primary',
            size: 'lg',
            show_percentage: true %>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-sm text-gray-500 mb-1">Start Date</div>
            <div class="font-semibold text-gray-900"><%= @campaign.start_date.strftime("%b %d, %Y") %></div>
          </div>
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-sm text-gray-500 mb-1">End Date</div>
            <div class="font-semibold text-gray-900"><%= @campaign.end_date.strftime("%b %d, %Y") %></div>
          </div>
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-sm text-gray-500 mb-1">Duration</div>
            <div class="font-semibold text-gray-900">
              <% if @campaign.duration_in_days %>
                <%= @campaign.duration_in_days %> days
              <% else %>
                N/A
              <% end %>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Main Content Layout -->
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">

      <!-- Primary Content Area -->
      <div class="xl:col-span-2 space-y-8">

        <!-- Campaign Type Specific Content -->
        <% case @campaign.campaign_type %>
        <% when 'email' %>
          <div class="bg-white rounded-xl shadow-sm p-8">
            <div class="flex items-center justify-between mb-8">
              <div class="flex items-center space-x-4">
                <div class="p-3 bg-blue-100 rounded-lg">
                  <%= render 'shared/icons/heroicon', name: 'envelope', class: 'w-6 h-6 text-blue-600' %>
                </div>
                <div>
                  <h2 class="text-2xl font-semibold text-gray-900">Email Campaign</h2>
                  <p class="text-gray-500">Email marketing configuration and content</p>
                </div>
              </div>
              <% if @campaign.email_campaign %>
                <%= render 'dashboard/status_badge',
                    status: 'configured',
                    text: 'Configured',
                    variant: 'soft',
                    size: 'sm' %>
              <% else %>
                <%= render 'dashboard/status_badge',
                    status: 'setup_required',
                    text: 'Setup Required',
                    variant: 'soft',
                    size: 'sm' %>
              <% end %>
            </div>

            <% if @campaign.email_campaign %>
              <div class="space-y-6">
                <!-- Email Details Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Subject Line</label>
                      <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-900 font-medium"><%= @campaign.email_campaign.subject_line %></p>
                      </div>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">From Information</label>
                      <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-900"><%= @campaign.email_campaign.from_name %></p>
                        <p class="text-gray-500 text-sm"><%= @campaign.email_campaign.from_email %></p>
                      </div>
                    </div>

                    <% if @campaign.email_campaign.preview_text.present? %>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Preview Text</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                          <p class="text-gray-900"><%= @campaign.email_campaign.preview_text %></p>
                        </div>
                      </div>
                    <% end %>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Content Preview</label>
                    <div class="bg-gray-50 rounded-lg p-4 h-48 overflow-y-auto">
                      <p class="text-gray-900 text-sm leading-relaxed"><%= @campaign.email_campaign.preview_snippet(300) %></p>
                    </div>
                  </div>
                </div>

                <!-- Email Statistics -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
                  <h3 class="text-lg font-semibold text-gray-900 mb-4">Email Statistics</h3>
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div class="text-center">
                      <div class="text-2xl font-bold text-blue-600"><%= number_with_delimiter(@campaign.email_campaign.recipient_count) %></div>
                      <div class="text-sm text-gray-600">Recipients</div>
                    </div>
                    <div class="text-center">
                      <div class="text-2xl font-bold text-indigo-600"><%= @campaign.email_campaign.estimated_send_time %> min</div>
                      <div class="text-sm text-gray-600">Est. Send Time</div>
                    </div>
                  </div>
                </div>
              </div>
            <% else %>
              <div class="text-center py-12">
                <div class="p-4 bg-gray-100 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                  <%= render 'shared/icons/heroicon', name: 'envelope', class: 'w-10 h-10 text-gray-400' %>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-3">Email Content Not Set Up</h3>
                <p class="text-gray-600 mb-6 max-w-md mx-auto">Configure your email content, subject line, and recipient list to start sending campaigns.</p>
                <% if @campaign.email_campaign %>
                  <%= link_to campaign_email_content_path(@campaign),
                      class: "inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md" do %>
                    <%= render 'shared/icons/heroicon', name: 'pencil', class: 'w-5 h-5 mr-2' %>
                    Edit Email Content
                  <% end %>
                <% else %>
                  <%= link_to new_campaign_email_content_path(@campaign),
                      class: "inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md" do %>
                    <%= render 'shared/icons/heroicon', name: 'plus', class: 'w-5 h-5 mr-2' %>
                    Setup Email Content
                  <% end %>
                <% end %>
              </div>
            <% end %>
          </div>

        <% when 'social' %>
          <div class="bg-white rounded-xl shadow-sm p-8">
            <div class="flex items-center justify-between mb-8">
              <div class="flex items-center space-x-4">
                <div class="p-3 bg-green-100 rounded-lg">
                  <%= render 'shared/icons/heroicon', name: 'chat-bubble-left-right', class: 'w-6 h-6 text-green-600' %>
                </div>
                <div>
                  <h2 class="text-2xl font-semibold text-gray-900">Social Media Campaign</h2>
                  <p class="text-gray-500">Social media content and platform management</p>
                </div>
              </div>
              <% if @campaign.social_campaign %>
                <%= render 'dashboard/status_badge',
                    status: 'configured',
                    text: 'Configured',
                    variant: 'soft',
                    size: 'sm' %>
              <% else %>
                <%= render 'dashboard/status_badge',
                    status: 'setup_required',
                    text: 'Setup Required',
                    variant: 'soft',
                    size: 'sm' %>
              <% end %>
            </div>

        <% if @campaign.social_campaign %>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Platforms</label>
              <div class="flex flex-wrap gap-2">
                <% @campaign.social_campaign.platforms.each do |platform| %>
                  <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full bg-green-100 text-green-800">
                    <%= platform.titleize %>
                  </span>
                <% end %>
              </div>
            </div>

            <% if @campaign.social_campaign.hashtags.present? %>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Hashtags</label>
                <div class="flex flex-wrap gap-2">
                  <% @campaign.social_campaign.hashtag_list.each do |hashtag| %>
                    <span class="inline-flex px-2 py-1 text-xs font-medium rounded bg-blue-100 text-blue-800">
                      <%= hashtag.start_with?('#') ? hashtag : "##{hashtag}" %>
                    </span>
                  <% end %>
                </div>
              </div>
            <% end %>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Content Variants</label>
              <div class="space-y-3">
                <% @campaign.social_campaign.content_variants.each do |platform, content| %>
                  <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-sm font-medium text-gray-900"><%= platform.titleize %></span>
                      <span class="text-xs text-gray-500"><%= content.length %> characters</span>
                    </div>
                    <p class="text-sm text-gray-700"><%= truncate(content, length: 150) %></p>
                  </div>
                <% end %>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
              <div>
                <span class="text-sm text-gray-500">Est. Reach</span>
                <p class="text-lg font-semibold text-gray-900"><%= number_with_delimiter(@campaign.social_campaign.estimated_reach) %></p>
              </div>
              <div>
                <span class="text-sm text-gray-500">Scheduled Posts</span>
                <p class="text-lg font-semibold text-gray-900"><%= @campaign.social_campaign.scheduled_posts.count %></p>
              </div>
            </div>
            
            <div class="pt-4 border-t border-gray-200 mt-4">
              <%= link_to campaign_social_content_path(@campaign), 
                  class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Manage Social Content
              <% end %>
            </div>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Social Content Not Set Up</h3>
            <p class="text-gray-600 mb-4">Configure your social media content and platforms.</p>
            <%= link_to new_campaign_social_content_path(@campaign), 
                class: "inline-flex items-center px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Setup Social Content
            <% end %>
          </div>
        <% end %>
      </div>

    <% when 'seo' %>
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-gray-900 flex items-center">
            <svg class="w-6 h-6 mr-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            SEO Campaign Details
          </h2>
          <% if @campaign.seo_campaign %>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
              Configured
            </span>
          <% else %>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
              Setup Required
            </span>
          <% end %>
        </div>

        <% if @campaign.seo_campaign %>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Target Keywords</label>
              <div class="flex flex-wrap gap-2">
                <% @campaign.seo_campaign.keyword_list.each do |keyword| %>
                  <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full bg-purple-100 text-purple-800">
                    <%= keyword %>
                  </span>
                <% end %>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Meta Title</label>
              <p class="text-gray-900 bg-gray-50 rounded-lg p-3"><%= @campaign.seo_campaign.meta_title %></p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Meta Description</label>
              <p class="text-gray-900 bg-gray-50 rounded-lg p-3"><%= @campaign.seo_campaign.meta_description %></p>
            </div>

            <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
              <div>
                <span class="text-sm text-gray-500">Optimization Score</span>
                <p class="text-lg font-semibold text-gray-900"><%= @campaign.seo_campaign.optimization_score %>%</p>
              </div>
              <div>
                <span class="text-sm text-gray-500">Est. Traffic Increase</span>
                <p class="text-lg font-semibold text-gray-900">+<%= number_with_delimiter(@campaign.seo_campaign.estimated_traffic_increase) %></p>
              </div>
            </div>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">SEO Strategy Not Set Up</h3>
            <p class="text-gray-600 mb-4">Configure your SEO strategy and target keywords.</p>
            <button class="inline-flex items-center px-4 py-2 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Setup SEO Strategy
            </button>
          </div>
        <% end %>
      </div>

    <% when 'multi_channel' %>
      <div class="bg-white rounded-xl shadow-sm p-8">
        <div class="flex items-center justify-between mb-8">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-md">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <div>
              <h2 class="text-2xl font-semibold text-gray-900">Multi-Channel Campaign</h2>
              <p class="text-gray-500">Coordinate marketing across multiple channels for maximum impact</p>
            </div>
          </div>
          <span class="inline-flex items-center px-3 py-1.5 text-sm font-semibold rounded-full bg-gradient-to-r from-orange-100 to-red-100 text-orange-800">
            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
            </svg>
            Advanced
          </span>
        </div>

        <!-- Channel Status Overview -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8">
          <!-- Email Channel -->
          <%= link_to (@campaign.email_campaign.present? ? campaign_email_content_path(@campaign) : new_campaign_email_content_path(@campaign)),
              class: "group relative bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer block h-full flex flex-col",
              data: { turbo_frame: "_top" } do %>
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <span class="font-semibold text-blue-900">Email</span>
              </div>
              <svg class="w-4 h-4 text-blue-400 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
              </svg>
            </div>

            <div class="flex-1 flex flex-col justify-between">
              <% if @campaign.email_campaign.present? %>
                <div class="space-y-2">
                  <span class="inline-flex items-center px-2.5 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    Configured
                  </span>
                  <p class="text-sm text-blue-700 font-medium leading-tight"><%= truncate(@campaign.email_campaign.subject_line, length: 40) %></p>
                </div>
                <p class="text-xs text-blue-600 mt-auto">Click to manage email content</p>
              <% else %>
                <div class="space-y-2">
                  <span class="inline-flex items-center px-2.5 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-700">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                    Not Set Up
                  </span>
                  <p class="text-sm text-blue-700 font-medium">Email Campaign</p>
                </div>
                <p class="text-xs text-blue-600 mt-auto">Click to setup email content</p>
              <% end %>
            </div>
          <% end %>

          <!-- Social Channel -->
          <div class="group relative bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 shadow-sm opacity-75 cursor-not-allowed h-full flex flex-col">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-green-100 rounded-lg">
                  <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                  </svg>
                </div>
                <span class="font-semibold text-green-900">Social</span>
              </div>
              <div class="p-1 bg-gray-200 rounded-full">
                <svg class="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                </svg>
              </div>
            </div>

            <div class="flex-1 flex flex-col justify-between">
              <% if @campaign.social_campaign.present? %>
                <div class="space-y-2">
                  <span class="inline-flex items-center px-2.5 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    Configured
                  </span>
                  <p class="text-sm text-green-700 font-medium">Social Media Posts</p>
                </div>
                <p class="text-xs text-green-600 mt-auto">Click to manage social content</p>
              <% else %>
                <div class="space-y-2">
                  <span class="inline-flex items-center px-2.5 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-700">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                    Not Set Up
                  </span>
                  <p class="text-sm text-green-700 font-medium">Social Media Campaign</p>
                </div>
                <p class="text-xs text-gray-500 mt-auto">Coming soon</p>
              <% end %>
            </div>
          </div>

          <!-- SEO Channel -->
          <div class="group relative bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-6 shadow-sm opacity-75 cursor-not-allowed">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                  <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                </div>
                <span class="font-semibold text-purple-900">SEO</span>
              </div>
              <svg class="w-4 h-4 text-purple-400 group-hover:text-purple-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
              </svg>
            </div>

            <% if @campaign.seo_campaign.present? %>
              <div class="space-y-2">
                <span class="inline-flex items-center px-2.5 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                  Configured
                </span>
                <p class="text-sm text-purple-700 font-medium">SEO Strategy</p>
                <p class="text-xs text-purple-600">Click to manage SEO content</p>
              </div>
            <% else %>
              <div class="space-y-2">
                <span class="inline-flex items-center px-2.5 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-700">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                  </svg>
                  Not Set Up
                </span>
                <p class="text-sm text-purple-700 font-medium">SEO Campaign</p>
                <p class="text-xs text-purple-600">Click to setup SEO strategy</p>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Multi-Channel Strategy Actions -->
        <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-6">
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Multi-Channel Strategy</h3>
              <p class="text-gray-600 text-sm leading-relaxed">Configure and coordinate your marketing channels for maximum impact and reach.</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-3 lg:flex-shrink-0">
              <% if @campaign.email_campaign.blank? %>
                <%= link_to new_campaign_email_content_path(@campaign),
                    class: "inline-flex items-center justify-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                  </svg>
                  <span class="whitespace-nowrap">Setup Email</span>
                <% end %>
              <% end %>

              <button type="button"
                      data-controller="modal"
                      data-action="click->modal#open"
                      data-modal-target-value="#multiChannelModal"
                      class="inline-flex items-center justify-center px-6 py-2.5 bg-gradient-to-r from-orange-600 to-red-600 text-white font-semibold rounded-lg hover:from-orange-700 hover:to-red-700 transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
                <span class="whitespace-nowrap">Configure Strategy</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Sidebar -->
  <div class="space-y-8">

    <!-- Campaign Stats -->
    <div class="bg-white rounded-xl shadow-sm p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Campaign Statistics</h3>

      <% if @performance_summary.present? && @performance_summary.any? %>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Total Impressions</span>
            <span class="font-semibold text-gray-900"><%= number_with_delimiter(@performance_summary[:total_impressions]) %></span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Total Clicks</span>
            <span class="font-semibold text-gray-900"><%= number_with_delimiter(@performance_summary[:total_clicks]) %></span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Conversions</span>
            <span class="font-semibold text-gray-900"><%= number_with_delimiter(@performance_summary[:total_conversions]) %></span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Revenue</span>
            <span class="font-semibold text-green-600">$<%= number_with_precision(@performance_summary[:total_revenue], precision: 2, delimiter: ',') %></span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Cost</span>
            <span class="font-semibold text-red-600">$<%= number_with_precision(@performance_summary[:total_cost], precision: 2, delimiter: ',') %></span>
          </div>

          <hr class="border-gray-200">

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Click Rate</span>
            <span class="font-semibold text-blue-600"><%= @performance_summary[:average_ctr] %>%</span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Conversion Rate</span>
            <span class="font-semibold text-purple-600"><%= @performance_summary[:average_conversion_rate] %>%</span>
          </div>
        </div>
      <% else %>
        <div class="text-center py-8">
          <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <h4 class="text-lg font-medium text-gray-900 mb-2">No Performance Data</h4>
          <p class="text-gray-600 text-sm">Campaign metrics will appear here once the campaign is active and generating data.</p>
        </div>
      <% end %>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-xl shadow-sm p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>

      <div class="space-y-3">
        <% if @campaign.can_be_activated? %>
          <%= link_to activate_campaign_path(@campaign),
              class: "w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors",
              data: {
                turbo_method: :patch,
                turbo_confirm: "Are you sure you want to activate this campaign?"
              } do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M7 16a3 3 0 006 0v-2"></path>
            </svg>
            Activate Campaign
          <% end %>
        <% elsif @campaign.active? %>
          <%= link_to pause_campaign_path(@campaign),
              class: "w-full inline-flex items-center justify-center px-4 py-2 bg-yellow-600 text-white font-medium rounded-lg hover:bg-yellow-700 transition-colors",
              data: {
                turbo_method: :patch,
                turbo_confirm: "Are you sure you want to pause this campaign?"
              } do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Pause Campaign
          <% end %>
        <% end %>

        <%= link_to edit_campaign_path(@campaign),
            class: "w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          Edit Campaign
        <% end %>

        <%= link_to campaigns_path,
            class: "w-full inline-flex items-center justify-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to Campaigns
        <% end %>
      </div>
    </div>

    <!-- Campaign Timeline -->
    <% if @campaign.start_date || @campaign.end_date %>
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Campaign Timeline</h3>

        <div class="space-y-4">
          <% if @campaign.start_date %>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
              <div>
                <p class="text-sm font-medium text-gray-900">Campaign Start</p>
                <p class="text-xs text-gray-500"><%= @campaign.start_date.strftime("%B %d, %Y") %></p>
              </div>
            </div>
          <% end %>

          <% if @campaign.start_date && @campaign.end_date %>
            <% mid_date = @campaign.start_date + (@campaign.duration_in_days / 2).days %>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
              <div>
                <p class="text-sm font-medium text-gray-900">Mid-Campaign Review</p>
                <p class="text-xs text-gray-500"><%= mid_date.strftime("%B %d, %Y") %></p>
              </div>
            </div>
          <% end %>

          <% if @campaign.end_date %>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
              <div>
                <p class="text-sm font-medium text-gray-900">Campaign End</p>
                <p class="text-xs text-gray-500"><%= @campaign.end_date.strftime("%B %d, %Y") %></p>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>

<!-- Multi-Channel Strategy Modal -->
<div id="multiChannelModal"
     data-controller="modal"
     data-modal-target="container"
     data-action="click->modal#closeOnBackdrop keydown@window->modal#closeOnEscape"
     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-4 sm:top-20 mx-auto p-4 sm:p-6 border w-full max-w-4xl shadow-lg rounded-xl bg-white max-h-[90vh] overflow-y-auto"
       data-action="click->modal#preventClose">
    <div class="mt-3">
      <!-- Modal Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Configure Multi-Channel Strategy</h3>
            <p class="text-sm text-gray-600">Set up and coordinate your marketing channels</p>
          </div>
        </div>
        <button type="button"
                data-action="click->modal#close"
                class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="py-6">
        <div class="space-y-6">
          <!-- Channel Setup Options -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            <!-- Email Setup -->
            <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
              <div class="flex items-center space-x-3 mb-3">
                <div class="p-2 bg-blue-100 rounded-lg">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                  </svg>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900">Email Campaign</h4>
                  <% if @campaign.email_campaign.present? %>
                    <span class="text-xs text-green-600">✓ Configured</span>
                  <% else %>
                    <span class="text-xs text-gray-500">Not set up</span>
                  <% end %>
                </div>
              </div>
              <p class="text-sm text-gray-600 mb-4">Create and send targeted email campaigns to your audience.</p>
              <% if @campaign.email_campaign.present? %>
                <%= link_to campaign_email_content_path(@campaign),
                    class: "w-full inline-flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 font-medium rounded-lg hover:bg-blue-200 transition-colors" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                  </svg>
                  Manage Email
                <% end %>
              <% else %>
                <%= link_to new_campaign_email_content_path(@campaign),
                    class: "w-full inline-flex items-center justify-center px-3 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                  </svg>
                  Setup Email
                <% end %>
              <% end %>
            </div>

            <!-- Social Setup -->
            <div class="border border-gray-200 rounded-lg p-4 hover:border-green-300 transition-colors">
              <div class="flex items-center space-x-3 mb-3">
                <div class="p-2 bg-green-100 rounded-lg">
                  <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"/>
                  </svg>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900">Social Media</h4>
                  <% if @campaign.social_campaign.present? %>
                    <span class="text-xs text-green-600">✓ Configured</span>
                  <% else %>
                    <span class="text-xs text-gray-500">Not set up</span>
                  <% end %>
                </div>
              </div>
              <p class="text-sm text-gray-600 mb-4">Create social media posts across multiple platforms.</p>
              <button type="button"
                      class="w-full inline-flex items-center justify-center px-3 py-2 bg-gray-100 text-gray-500 font-medium rounded-lg cursor-not-allowed"
                      disabled>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                Coming Soon
              </button>
            </div>

            <!-- SEO Setup -->
            <div class="border border-gray-200 rounded-lg p-4 hover:border-purple-300 transition-colors">
              <div class="flex items-center space-x-3 mb-3">
                <div class="p-2 bg-purple-100 rounded-lg">
                  <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                  </svg>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900">SEO Strategy</h4>
                  <% if @campaign.seo_campaign.present? %>
                    <span class="text-xs text-green-600">✓ Configured</span>
                  <% else %>
                    <span class="text-xs text-gray-500">Not set up</span>
                  <% end %>
                </div>
              </div>
              <p class="text-sm text-gray-600 mb-4">Optimize content for search engines and organic traffic.</p>
              <button type="button"
                      class="w-full inline-flex items-center justify-center px-3 py-2 bg-gray-100 text-gray-500 font-medium rounded-lg cursor-not-allowed"
                      disabled>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                Coming Soon
              </button>
            </div>
          </div>

          <!-- Strategy Tips -->
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
            <h4 class="font-semibold text-gray-900 mb-3">Multi-Channel Strategy Tips</h4>
            <ul class="space-y-2 text-sm text-gray-700">
              <li class="flex items-start space-x-2">
                <svg class="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                <span>Start with email campaigns for immediate reach and engagement</span>
              </li>
              <li class="flex items-start space-x-2">
                <svg class="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                <span>Maintain consistent messaging across all channels</span>
              </li>
              <li class="flex items-start space-x-2">
                <svg class="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                <span>Coordinate timing for maximum impact and reach</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
        <button type="button"
                data-action="click->modal#close"
                class="px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors">
          Close
        </button>
      </div>
    </div>
  </div>
</div>
