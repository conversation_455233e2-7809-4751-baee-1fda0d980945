<%# Platform-specific content form %>
<%
  # Handle both string content and hash content (from AI generation)
  content_text = case content
                 when Hash
                   content['content'] || content[:content] || ''
                 when String
                   content || ''
                 else
                   ''
                 end
%>

<div class="platform-content-form border border-gray-200 rounded-lg" data-platform="<%= platform %>">
  <div class="bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between">
    <div class="flex items-center space-x-3">
      <%= render "shared/platform_icons/#{platform}", class: "w-6 h-6" %>
      <h4 class="text-sm font-medium text-gray-900"><%= platform.titleize %> Content</h4>
    </div>

    <div class="flex items-center space-x-2 text-xs text-gray-500">
      <span>Limit: <%= limit[:character_limit] %> chars</span>
      <span>•</span>
      <span><%= limit[:hashtag_limit] %> hashtags</span>
    </div>
  </div>

  <div class="p-4 space-y-4">
    <!-- Content Textarea -->
    <div>
      <%= form.text_area "content_variants[#{platform}]",
          value: content_text,
          placeholder: "Enter your #{platform} content here...",
          rows: 4,
          class: "block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 content-textarea",
          data: {
            platform: platform,
            limit: limit[:character_limit],
            hashtag_limit: limit[:hashtag_limit]
          } %>

      <!-- Character Counter -->
      <div class="mt-2 flex justify-between items-center">
        <div class="flex items-center space-x-4 text-xs">
          <span class="text-gray-500">
            Characters:
            <span class="char-count font-medium" data-platform="<%= platform %>">
              <%= content_text.length %>
            </span> / <%= limit[:character_limit] %>
          </span>

          <span class="text-gray-500">
            Hashtags:
            <span class="hashtag-count font-medium" data-platform="<%= platform %>">
              <%= content_text.scan(/#\w+/).count %>
            </span> / <%= limit[:hashtag_limit] %>
          </span>
        </div>
        
        <!-- Character Limit Warning -->
        <div class="char-warning hidden text-xs text-red-600" data-platform="<%= platform %>">
          Content exceeds character limit
        </div>
      </div>
    </div>

    <!-- Platform-specific Features -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      
      <!-- Quick Actions -->
      <div>
        <label class="block text-xs font-medium text-gray-500 mb-2">Quick Actions</label>
        <div class="flex flex-wrap gap-2">
          
          <!-- Add Emoji -->
          <button type="button" 
                  class="inline-flex items-center px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50 add-emoji-btn"
                  data-platform="<%= platform %>">
            😊 Add Emoji
          </button>
          
          <!-- Preview Post -->
          <button type="button" 
                  class="inline-flex items-center px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50 preview-btn"
                  data-platform="<%= platform %>">
            👁️ Preview
          </button>
          
          <!-- Schedule Post -->
          <button type="button" 
                  class="inline-flex items-center px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50 schedule-btn"
                  data-platform="<%= platform %>">
            ⏰ Schedule
          </button>
        </div>
      </div>

      <!-- Platform-specific Options -->
      <div>
        <label class="block text-xs font-medium text-gray-500 mb-2">Platform Options</label>
        
        <% case platform %>
        <% when 'twitter' %>
          <div class="space-y-2">
            <label class="flex items-center">
              <input type="checkbox" 
                     name="social_campaign[social_settings][twitter][thread]" 
                     class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
              <span class="ml-2 text-xs text-gray-700">Create as thread</span>
            </label>
            
            <label class="flex items-center">
              <input type="checkbox" 
                     name="social_campaign[social_settings][twitter][replies_restricted]" 
                     class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
              <span class="ml-2 text-xs text-gray-700">Restrict replies</span>
            </label>
          </div>
          
        <% when 'facebook' %>
          <div class="space-y-2">
            <label class="flex items-center">
              <input type="checkbox" 
                     name="social_campaign[social_settings][facebook][boost_post]" 
                     class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
              <span class="ml-2 text-xs text-gray-700">Boost post</span>
            </label>
            
            <select name="social_campaign[social_settings][facebook][audience]" 
                    class="mt-1 block w-full text-xs border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
              <option value="public">Public</option>
              <option value="friends">Friends</option>
              <option value="custom">Custom</option>
            </select>
          </div>
          
        <% when 'instagram' %>
          <div class="space-y-2">
            <label class="flex items-center">
              <input type="checkbox" 
                     name="social_campaign[social_settings][instagram][story_also]" 
                     class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
              <span class="ml-2 text-xs text-gray-700">Also post to Story</span>
            </label>
            
            <label class="flex items-center">
              <input type="checkbox" 
                     name="social_campaign[social_settings][instagram][shopping_tags]" 
                     class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
              <span class="ml-2 text-xs text-gray-700">Add shopping tags</span>
            </label>
          </div>
          
        <% when 'linkedin' %>
          <div class="space-y-2">
            <select name="social_campaign[social_settings][linkedin][post_type]" 
                    class="block w-full text-xs border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
              <option value="personal">Personal post</option>
              <option value="company">Company post</option>
              <option value="article">Article</option>
            </select>
            
            <label class="flex items-center">
              <input type="checkbox" 
                     name="social_campaign[social_settings][linkedin][notify_network]" 
                     class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
              <span class="ml-2 text-xs text-gray-700">Notify network</span>
            </label>
          </div>
          
        <% when 'tiktok' %>
          <div class="space-y-2">
            <label class="flex items-center">
              <input type="checkbox" 
                     name="social_campaign[social_settings][tiktok][allow_comments]" 
                     class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
              <span class="ml-2 text-xs text-gray-700">Allow comments</span>
            </label>
            
            <label class="flex items-center">
              <input type="checkbox" 
                     name="social_campaign[social_settings][tiktok][allow_duet]" 
                     class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
              <span class="ml-2 text-xs text-gray-700">Allow duet/stitch</span>
            </label>
          </div>
          
        <% when 'youtube' %>
          <div class="space-y-2">
            <select name="social_campaign[social_settings][youtube][visibility]" 
                    class="block w-full text-xs border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
              <option value="public">Public</option>
              <option value="unlisted">Unlisted</option>
              <option value="private">Private</option>
            </select>
            
            <label class="flex items-center">
              <input type="checkbox" 
                     name="social_campaign[social_settings][youtube][monetization]" 
                     class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
              <span class="ml-2 text-xs text-gray-700">Enable monetization</span>
            </label>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Content Suggestions -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
      <h5 class="text-xs font-medium text-blue-900 mb-2">
        <%= render 'shared/icons/heroicon', name: 'light-bulb', class: 'inline w-3 h-3 mr-1' %>
        <%= platform.titleize %> Best Practices
      </h5>
      
      <ul class="text-xs text-blue-800 space-y-1">
        <% case platform %>
        <% when 'twitter' %>
          <li>• Use 1-2 hashtags for optimal reach</li>
          <li>• Ask questions to boost engagement</li>
          <li>• Tweet during peak hours (9-10 AM, 7-9 PM)</li>
          
        <% when 'facebook' %>
          <li>• Posts with 80 characters get 66% more engagement</li>
          <li>• Use native video for 135% more reach</li>
          <li>• Post when your audience is most active</li>
          
        <% when 'instagram' %>
          <li>• Use all 30 hashtags for maximum discovery</li>
          <li>• Mix popular and niche hashtags</li>
          <li>• Post high-quality visual content</li>
          
        <% when 'linkedin' %>
          <li>• Professional tone performs better</li>
          <li>• Industry insights get more engagement</li>
          <li>• Post during business hours for B2B content</li>
          
        <% when 'tiktok' %>
          <li>• Hook viewers in first 3 seconds</li>
          <li>• Use trending sounds and effects</li>
          <li>• Keep content authentic and entertaining</li>
          
        <% when 'youtube' %>
          <li>• Descriptive titles improve discoverability</li>
          <li>• Include relevant keywords naturally</li>
          <li>• Encourage viewers to like and subscribe</li>
        <% end %>
      </ul>
    </div>
  </div>
</div>

<!-- Platform Content JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const textarea = document.querySelector(`[data-platform="<%= platform %>"].content-textarea`);
  const charCount = document.querySelector(`[data-platform="<%= platform %>"].char-count`);
  const hashtagCount = document.querySelector(`[data-platform="<%= platform %>"].hashtag-count`);
  const charWarning = document.querySelector(`[data-platform="<%= platform %>"].char-warning`);
  
  if (textarea) {
    textarea.addEventListener('input', function() {
      const content = this.value;
      const chars = content.length;
      const hashtags = (content.match(/#\w+/g) || []).length;
      const limit = parseInt(this.dataset.limit);
      const hashtagLimit = parseInt(this.dataset.hashtagLimit);
      
      // Update counters
      if (charCount) charCount.textContent = chars;
      if (hashtagCount) hashtagCount.textContent = hashtags;
      
      // Show/hide warnings
      if (charWarning) {
        if (chars > limit) {
          charWarning.classList.remove('hidden');
          charCount.classList.add('text-red-600');
        } else {
          charWarning.classList.add('hidden');
          charCount.classList.remove('text-red-600');
        }
      }
      
      // Hashtag warning
      if (hashtagCount) {
        if (hashtags > hashtagLimit) {
          hashtagCount.classList.add('text-red-600');
        } else {
          hashtagCount.classList.remove('text-red-600');
        }
      }
    });
  }
  
  // Add emoji functionality
  const emojiBtn = document.querySelector(`[data-platform="<%= platform %>"].add-emoji-btn`);
  if (emojiBtn) {
    emojiBtn.addEventListener('click', function() {
      const emojis = ['🚀', '✨', '💡', '🎯', '📈', '🔥', '💪', '🌟', '🎉', '👏'];
      const randomEmoji = emojis[Math.floor(Math.random() * emojis.length)];
      
      if (textarea) {
        const cursorPos = textarea.selectionStart;
        const textBefore = textarea.value.substring(0, cursorPos);
        const textAfter = textarea.value.substring(cursorPos);
        
        textarea.value = textBefore + randomEmoji + textAfter;
        textarea.setSelectionRange(cursorPos + 2, cursorPos + 2);
        textarea.focus();
        
        // Trigger input event to update counters
        textarea.dispatchEvent(new Event('input'));
      }
    });
  }
  
  // Preview functionality
  const previewBtn = document.querySelector(`[data-platform="<%= platform %>"].preview-btn`);
  if (previewBtn) {
    previewBtn.addEventListener('click', function() {
      const content = textarea.value;
      
      // Make AJAX call to get preview
      fetch(`<%= campaign_social_content_preview_path(@campaign) %>?platform=<%= platform %>&content=${encodeURIComponent(content)}`)
        .then(response => response.json())
        .then(data => {
          // Show preview in modal or sidebar
          showContentPreview(data);
        })
        .catch(error => {
          console.error('Preview error:', error);
        });
    });
  }
  
  // Schedule functionality
  const scheduleBtn = document.querySelector(`[data-platform="<%= platform %>"].schedule-btn`);
  if (scheduleBtn) {
    scheduleBtn.addEventListener('click', function() {
      showScheduleModal('<%= platform %>', textarea.value);
    });
  }
});

function showContentPreview(data) {
  // Implementation for showing preview in modal/sidebar
  const previewArea = document.getElementById('content-preview');
  if (previewArea) {
    previewArea.innerHTML = data.preview_html;
  }
}

function showScheduleModal(platform, content) {
  // Implementation for schedule modal
  const modal = document.createElement('div');
  modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
  modal.innerHTML = `
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Schedule ${platform.charAt(0).toUpperCase() + platform.slice(1)} Post</h3>
      
      <form action="<%= campaign_social_content_schedule_path(@campaign) %>" method="post">
        <input type="hidden" name="authenticity_token" value="<%= form_authenticity_token %>">
        <input type="hidden" name="platform" value="${platform}">
        <input type="hidden" name="content" value="${content}">
        
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Schedule Date & Time</label>
          <input type="datetime-local" 
                 name="scheduled_at" 
                 class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                 required>
        </div>
        
        <div class="flex justify-end space-x-3">
          <button type="button" 
                  onclick="this.closest('.fixed').remove()"
                  class="px-4 py-2 text-sm text-gray-700 bg-gray-200 rounded hover:bg-gray-300">
            Cancel
          </button>
          <button type="submit" 
                  class="px-4 py-2 text-sm text-white bg-indigo-600 rounded hover:bg-indigo-700">
            Schedule Post
          </button>
        </div>
      </form>
    </div>
  `;
  
  document.body.appendChild(modal);
}
</script>
