<%# Social Content Creation Interface %>
<% content_for :title, "Social Content - #{@campaign.name}" %>

<div class="bg-white">
  <!-- Header -->
  <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <%= link_to campaign_path(@campaign), class: "text-gray-500 hover:text-gray-700" do %>
          <%= render 'shared/icons/heroicon', name: 'arrow-left', class: 'w-5 h-5' %>
        <% end %>
        
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Social Content</h1>
          <p class="text-sm text-gray-500"><%= @campaign.name %> • <%= @campaign.campaign_type.titleize %> Campaign</p>
        </div>
      </div>

      <div class="flex items-center space-x-3">
        <!-- Scheduling Calendar -->
        <%= link_to campaign_social_scheduling_index_path(@campaign),
            class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          <%= render 'shared/icons/heroicon', name: 'calendar', class: 'w-4 h-4 mr-2' %>
          Scheduling Calendar
        <% end %>

        <!-- AI Content Generation -->
        <%= button_to campaign_social_content_generate_ai_content_path(@campaign), 
            method: :post, 
            class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",
            data: { 
              confirm: "This will generate new AI content for all selected platforms. Continue?",
              turbo_confirm: "Generate AI Content"
            } do %>
          <%= render 'shared/icons/heroicon', name: 'sparkles', class: 'w-4 h-4 mr-2' %>
          Generate AI Content
        <% end %>

        <!-- Optimize Content -->
        <% if @social_campaign&.content_variants&.any? %>
          <%= button_to campaign_social_content_optimize_content_path(@campaign), 
              method: :post, 
              class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <%= render 'shared/icons/heroicon', name: 'arrow-trending-up', class: 'w-4 h-4 mr-2' %>
            Optimize Content
          <% end %>
        <% end %>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Content Area -->
      <div class="lg:col-span-2 space-y-8">
        
        <!-- Platform Selection -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Platform Selection</h2>
            <p class="text-sm text-gray-500">Choose platforms for your social media campaign</p>
          </div>
          
          <div class="p-6">
            <%= form_with model: [@campaign, @social_campaign], url: campaign_social_content_path(@campaign), method: :patch, local: true, data: { turbo: false }, class: "space-y-6" do |form| %>
              
              <!-- Platform Selection Grid -->
              <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                <% SocialCampaign::SUPPORTED_PLATFORMS.each do |platform| %>
                  <% platform_limit = SocialMediaAgentService::PLATFORM_LIMITS[platform] %>
                  <% is_selected = @social_campaign&.platforms&.include?(platform) %>
                  
                  <label class="relative cursor-pointer">
                    <%= form.check_box :platforms, 
                        { multiple: true, checked: is_selected, class: "sr-only platform-checkbox", data: { platform: platform } }, 
                        platform, "" %>
                    
                    <div class="<%= is_selected ? 'ring-2 ring-indigo-500 bg-indigo-50' : 'ring-1 ring-gray-300' %> rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                          <%= render "shared/platform_icons/#{platform}", class: "w-8 h-8" %>
                        </div>
                        <div class="flex-1 min-w-0">
                          <h3 class="text-sm font-medium text-gray-900"><%= platform.titleize %></h3>
                          <p class="text-xs text-gray-500">
                            <%= platform_limit[:character_limit] %> chars, 
                            <%= platform_limit[:hashtag_limit] %> hashtags
                          </p>
                        </div>
                      </div>
                      
                      <% if is_selected %>
                        <div class="absolute top-2 right-2">
                          <%= render 'shared/icons/heroicon', name: 'check-circle', class: 'w-5 h-5 text-indigo-600' %>
                        </div>
                      <% end %>
                    </div>
                  </label>
                <% end %>
              </div>

              <!-- Content Creation for Selected Platforms -->
              <div id="platform-content-areas" class="space-y-6">
                <% if @social_campaign&.platforms&.any? %>
                  <h3 class="text-lg font-medium text-gray-900">Platform Content</h3>
                  
                  <% @social_campaign.platforms.each do |platform| %>
                    <div class="platform-content-area" data-platform="<%= platform %>">
                      <%= render 'platform_content_form', 
                          form: form, 
                          platform: platform, 
                          content: @social_campaign.content_for_platform(platform),
                          limit: SocialMediaAgentService::PLATFORM_LIMITS[platform] %>
                    </div>
                  <% end %>
                <% else %>
                  <div class="text-center py-8 text-gray-500">
                    <p>Select platforms above to create content</p>
                  </div>
                <% end %>
              </div>

              <!-- Global Hashtags -->
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <label for="social_campaign_hashtags" class="block text-sm font-medium text-gray-700">
                    Global Hashtags
                  </label>
                  <%= button_to campaign_social_content_generate_hashtags_path(@campaign), 
                      method: :post,
                      class: "text-xs bg-indigo-100 text-indigo-700 px-2 py-1 rounded hover:bg-indigo-200" do %>
                    <%= render 'shared/icons/heroicon', name: 'sparkles', class: 'w-3 h-3 mr-1' %>
                    Generate AI Hashtags
                  <% end %>
                </div>
                
                <%= form.text_field :hashtags, 
                    placeholder: "#marketing #automation #ai",
                    class: "mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500" %>
                <p class="text-xs text-gray-500">Separate hashtags with spaces. These will be added to all platforms.</p>
              </div>

              <!-- Target Demographics -->
              <div class="space-y-4">
                <h4 class="text-sm font-medium text-gray-700">Target Demographics</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label class="block text-xs font-medium text-gray-500 mb-1">Age Range</label>
                    <%= form.text_field "target_demographics[age_range]",
                        value: @social_campaign&.target_demographics&.dig("age_range"),
                        placeholder: "25-45",
                        class: "block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500" %>
                  </div>
                  
                  <div>
                    <label class="block text-xs font-medium text-gray-500 mb-1">Interests</label>
                    <%= form.text_field "target_demographics[interests]",
                        value: @social_campaign&.target_demographics&.dig("interests")&.join(", "),
                        placeholder: "technology, business, marketing",
                        class: "block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500" %>
                  </div>
                  
                  <div>
                    <label class="block text-xs font-medium text-gray-500 mb-1">Location</label>
                    <%= form.text_field "target_demographics[location]",
                        value: @social_campaign&.target_demographics&.dig("location"),
                        placeholder: "United States, Canada",
                        class: "block w-full text-sm border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500" %>
                  </div>
                </div>
              </div>

              <!-- Save Button -->
              <div class="flex justify-end pt-6 border-t border-gray-200">
                <%= form.submit "Save Social Content", 
                    class: "inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        
        <!-- Content Preview -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Preview</h3>
          </div>
          
          <div class="p-6">
            <div id="content-preview" class="space-y-4">
              <p class="text-sm text-gray-500 text-center py-8">
                Select a platform and enter content to see preview
              </p>
            </div>
          </div>
        </div>

        <!-- Analytics Summary -->
        <% if @analytics_data.any? %>
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">Performance Summary</h3>
            </div>
            
            <div class="p-6 space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900">
                    <%= number_with_delimiter(@analytics_data[:total_impressions] || 0) %>
                  </div>
                  <div class="text-xs text-gray-500">Total Impressions</div>
                </div>
                
                <div class="text-center">
                  <div class="text-2xl font-bold text-gray-900">
                    <%= @analytics_data[:engagement_rate] || 0 %>%
                  </div>
                  <div class="text-xs text-gray-500">Engagement Rate</div>
                </div>
              </div>
              
              <div class="text-center">
                <div class="text-lg font-semibold text-gray-900">
                  <%= number_with_delimiter(@analytics_data[:total_engagements] || 0) %>
                </div>
                <div class="text-xs text-gray-500">Total Engagements</div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Scheduled Posts -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Scheduled Posts</h3>
          </div>
          
          <div class="p-6">
            <% if @scheduled_posts.any? %>
              <div class="space-y-3">
                <% @scheduled_posts.each_with_index do |post, index| %>
                  <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                      <%= render "shared/platform_icons/#{post['platform']}", class: "w-5 h-5" %>
                      <div>
                        <div class="text-sm font-medium text-gray-900">
                          <%= post['platform'].titleize %>
                        </div>
                        <div class="text-xs text-gray-500">
                          <%= DateTime.parse(post['scheduled_at']).strftime('%b %d, %Y at %I:%M %p') %>
                        </div>
                      </div>
                    </div>
                    
                    <button type="button" class="text-gray-400 hover:text-red-500">
                      <%= render 'shared/icons/heroicon', name: 'x-mark', class: 'w-4 h-4' %>
                    </button>
                  </div>
                <% end %>
              </div>
            <% else %>
              <p class="text-sm text-gray-500 text-center py-4">
                No scheduled posts yet
              </p>
            <% end %>
          </div>
        </div>

        <!-- Platform Limits Reference -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Platform Limits</h3>
          </div>
          
          <div class="p-6">
            <div class="space-y-3">
              <% SocialMediaAgentService::PLATFORM_LIMITS.each do |platform, limits| %>
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <%= render "shared/platform_icons/#{platform}", class: "w-4 h-4" %>
                    <span class="text-sm text-gray-700"><%= platform.titleize %></span>
                  </div>
                  <div class="text-xs text-gray-500">
                    <%= limits[:character_limit] %>c / <%= limits[:hashtag_limit] %>h
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for dynamic platform content -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const platformCheckboxes = document.querySelectorAll('.platform-checkbox');
  const contentAreas = document.getElementById('platform-content-areas');
  
  platformCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', function() {
      const platform = this.dataset.platform;
      const isChecked = this.checked;
      
      if (isChecked) {
        // Add content area for this platform
        addPlatformContentArea(platform);
      } else {
        // Remove content area for this platform
        removePlatformContentArea(platform);
      }
      
      updatePlatformSelection(this);
    });
  });
  
  function addPlatformContentArea(platform) {
    const existingArea = document.querySelector(`[data-platform="${platform}"]`);
    if (existingArea) return;
    
    // This would ideally fetch the form partial via AJAX
    // For now, we'll show a placeholder
    const contentArea = document.createElement('div');
    contentArea.className = 'platform-content-area';
    contentArea.dataset.platform = platform;
    contentArea.innerHTML = `
      <div class="border border-gray-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 mb-3">${platform.charAt(0).toUpperCase() + platform.slice(1)} Content</h4>
        <textarea 
          name="social_campaign[content_variants][${platform}]"
          placeholder="Enter your ${platform} content here..."
          class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
          rows="4"
        ></textarea>
        <div class="mt-2 flex justify-between text-xs text-gray-500">
          <span>Character count: <span class="char-count">0</span></span>
          <span>Limit: ${getPlatformLimit(platform)}</span>
        </div>
      </div>
    `;
    
    contentAreas.appendChild(contentArea);
    
    // Add character counting
    const textarea = contentArea.querySelector('textarea');
    const charCount = contentArea.querySelector('.char-count');
    
    textarea.addEventListener('input', function() {
      charCount.textContent = this.value.length;
      
      // Update preview
      updateContentPreview(platform, this.value);
    });
  }
  
  function removePlatformContentArea(platform) {
    const contentArea = document.querySelector(`[data-platform="${platform}"]`);
    if (contentArea) {
      contentArea.remove();
    }
  }
  
  function updatePlatformSelection(checkbox) {
    const label = checkbox.closest('label');
    const container = label.querySelector('div');
    
    if (checkbox.checked) {
      container.classList.add('ring-2', 'ring-indigo-500', 'bg-indigo-50');
      container.classList.remove('ring-1', 'ring-gray-300');
    } else {
      container.classList.remove('ring-2', 'ring-indigo-500', 'bg-indigo-50');
      container.classList.add('ring-1', 'ring-gray-300');
    }
  }
  
  function getPlatformLimit(platform) {
    const limits = {
      'twitter': 280,
      'facebook': 2200,
      'instagram': 2200,
      'linkedin': 3000,
      'tiktok': 150,
      'youtube': 5000
    };
    return limits[platform] || 280;
  }
  
  function updateContentPreview(platform, content) {
    // This would make an AJAX call to get the preview
    // For now, just show basic preview
    const preview = document.getElementById('content-preview');
    preview.innerHTML = `
      <div class="preview-item">
        <h4 class="text-sm font-medium text-gray-900 mb-2">${platform.charAt(0).toUpperCase() + platform.slice(1)} Preview</h4>
        <div class="bg-gray-50 p-3 rounded border">
          <p class="text-sm">${content}</p>
        </div>
      </div>
    `;
  }
});
</script>
