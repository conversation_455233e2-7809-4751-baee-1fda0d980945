<% content_for :title, "Dashboard" %>

<!-- Modern Dashboard Container -->
<div class="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 pb-12 relative overflow-hidden" data-controller="dashboard" data-dashboard-refresh-interval-value="30000">
  <!-- Animated Background Elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse"></div>
    <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-cyan-400/10 to-teal-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 4s;"></div>
  </div>

  <!-- Enhanced Header Section -->
  <div class="bg-white/95 backdrop-blur-sm sticky top-0 z-10 mb-0 shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="flex-1">
          <div class="flex items-center space-x-4 mb-2">
            <div class="p-3 bg-blue-600 rounded-lg shadow-sm">
              <%= render 'shared/icons/heroicon', name: 'presentation-chart-line', variant: 'solid', class: 'w-8 h-8 text-white' %>
            </div>
            <div>
              <h1 class="text-3xl font-bold text-gray-900">
                Good <%= Time.current.hour < 12 ? 'morning' : Time.current.hour < 18 ? 'afternoon' : 'evening' %>, <%= current_user.email.split('@').first.titleize %>
              </h1>
              <p class="text-gray-600 text-lg">AI Marketing Intelligence Dashboard</p>
            </div>
          </div>
        </div>

        <div class="flex items-center space-x-4 mt-6 lg:mt-0">
          <!-- Enhanced Time Range Selector -->
          <div class="flex items-center space-x-3 bg-white rounded-lg px-4 py-2 shadow-sm">
            <%= render 'shared/icons/heroicon', name: 'chart-bar', class: 'w-4 h-4 text-gray-500' %>
            <label class="text-sm text-gray-700 font-medium">Period:</label>
            <%= select_tag :time_range,
                options_for_select([
                  ['Last 7 days', '7d'],
                  ['Last 30 days', '30d'],
                  ['Last 90 days', '90d']
                ], '7d'),
                class: "text-sm border-0 bg-transparent text-gray-800 font-medium focus:ring-0 cursor-pointer" %>
          </div>

          <!-- Enhanced Action Buttons -->
          <div class="flex items-center space-x-2">
            <button class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200 shadow-sm" title="Refresh Dashboard">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            </button>

            <button class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200 shadow-sm" title="Dashboard Settings">
              <%= render 'shared/icons/heroicon', name: 'cog-6-tooth', class: 'w-5 h-5' %>
            </button>
          </div>

          <!-- Enhanced New Campaign Button -->
          <%= link_to new_campaign_path, class: "inline-flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <span>New Campaign</span>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Modern KPI Cards Section -->
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Overall Vibe Score -->
      <%= render 'dashboard/metric_card',
          title: 'Vibe Score',
          value: number_with_precision(@vibe_metrics[:overall_vibe_score], precision: 1),
          subtitle: 'Excellent engagement',
          trend: 'positive',
          trend_value: '+15.2%',
          icon: 'heart',
          color_scheme: 'vibrant_success' %>

      <!-- Emotional Resonance -->
      <%= render 'dashboard/metric_card',
          title: 'Emotional Resonance',
          value: number_with_precision(@emotional_resonance[:resonance_score], precision: 1),
          subtitle: "#{@emotional_resonance[:primary_emotion]} dominant",
          trend: 'positive',
          trend_value: 'Strong',
          icon: 'users',
          color_scheme: 'vibrant_purple' %>

      <!-- Authenticity Score -->
      <%= render 'dashboard/metric_card',
          title: 'Authenticity',
          value: number_with_precision(@authenticity_scores[:average_score], precision: 1),
          subtitle: "#{@authenticity_scores[:approval_rate]}% approval rate",
          trend: @authenticity_scores[:risk_assessment] == 'Low' ? 'positive' : 'neutral',
          trend_value: @authenticity_scores[:risk_assessment],
          icon: 'shield-check',
          color_scheme: @authenticity_scores[:risk_assessment] == 'Low' ? 'vibrant_blue' : 'vibrant_orange' %>

      <!-- Cultural Alignment -->
      <%= render 'dashboard/metric_card',
          title: 'Cultural Alignment',
          value: number_with_precision(@cultural_alignment[:alignment_score], precision: 1),
          subtitle: "#{@cultural_alignment[:cultural_moments_captured]} moments captured",
          trend: 'positive',
          trend_value: @cultural_alignment[:cultural_fit_rating],
          icon: 'globe-alt',
          color_scheme: 'vibrant_pink' %>
    </div>

    <!-- Additional Performance Metrics Row -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
      <!-- Total Campaigns -->
      <%= render 'dashboard/metric_card',
          title: 'Total Campaigns',
          value: number_with_delimiter(@campaign_stats[:total]),
          subtitle: "#{@campaign_stats[:active]} active",
          trend: 'positive',
          trend_value: "+#{@campaign_stats[:active]}",
          icon: 'chart-bar',
          color_scheme: 'vibrant_cyan' %>

      <!-- Total Budget -->
      <%= render 'dashboard/metric_card',
          title: 'Total Budget',
          value: "$#{number_with_delimiter(@budget_stats[:total_budget].round)}",
          subtitle: "$#{number_with_delimiter(@budget_stats[:spent_budget].round)} spent",
          trend: 'neutral',
          trend_value: "#{((@budget_stats[:spent_budget] / @budget_stats[:total_budget]) * 100).round}%",
          icon: 'currency-dollar',
          color_scheme: 'vibrant_emerald' %>

      <!-- Success Rate -->
      <%= render 'dashboard/metric_card',
          title: 'Success Rate',
          value: "#{@performance_metrics[:success_rate]}%",
          subtitle: 'Campaign performance',
          trend: 'positive',
          trend_value: @performance_metrics[:improvement_trend] || '+8.2%',
          icon: 'arrow-trending-up',
          color_scheme: 'vibrant_lime' %>

      <!-- ROI -->
      <%= render 'dashboard/metric_card',
          title: 'Average ROI',
          value: "#{@performance_metrics[:avg_roi]}%",
          subtitle: 'Return on investment',
          trend: 'positive',
          trend_value: '+12.5%',
          icon: 'banknotes',
          color_scheme: 'vibrant_gold' %>
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-16">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

      <!-- Left Column - Analytics -->
      <div class="lg:col-span-2 space-y-8">

        <!-- Performance Trend Chart -->
        <%= render 'dashboard/chart_container',
            title: 'Performance Trend',
            subtitle: 'Campaign performance over time',
            chart_type: 'line',
            height: 'h-80',
            data: @vibe_metrics[:vibe_performance_trend] do %>
          <% content_for :chart_footer do %>
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Average Score: <%= @vibe_metrics[:overall_vibe_score] %>/10</span>
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span class="text-gray-600">Vibe Score</span>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span class="text-gray-600">Engagement</span>
                </div>
              </div>
            </div>
          <% end %>
        <% end %>

        <!-- Sentiment Distribution -->
        <div class="bg-white rounded-xl shadow-sm">
          <div class="px-6 py-4">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Sentiment Distribution</h3>
                <p class="text-sm text-gray-600 mt-1">Campaign sentiment analysis</p>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                All Campaigns
              </span>
            </div>
          </div>

          <div class="p-6">
            <div class="space-y-6">
              <!-- Positive Sentiment -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                  <span class="text-gray-700 font-medium">Positive</span>
                </div>
                <div class="flex items-center space-x-4">
                  <%= render 'dashboard/progress_bar',
                      value: @vibe_metrics[:sentiment_distribution][:positive],
                      color: 'success',
                      size: 'sm',
                      show_percentage: false %>
                  <span class="text-gray-900 font-semibold w-12 text-right"><%= @vibe_metrics[:sentiment_distribution][:positive] %>%</span>
                </div>
              </div>

              <!-- Neutral Sentiment -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-4 h-4 bg-gray-400 rounded-full"></div>
                  <span class="text-gray-700 font-medium">Neutral</span>
                </div>
                <div class="flex items-center space-x-4">
                  <%= render 'dashboard/progress_bar',
                      value: @vibe_metrics[:sentiment_distribution][:neutral],
                      color: 'gray',
                      size: 'sm',
                      show_percentage: false %>
                  <span class="text-gray-900 font-semibold w-12 text-right"><%= @vibe_metrics[:sentiment_distribution][:neutral] %>%</span>
                </div>
              </div>

              <!-- Negative Sentiment -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-4 h-4 bg-red-400 rounded-full"></div>
                  <span class="text-gray-700 font-medium">Negative</span>
                </div>
                <div class="flex items-center space-x-4">
                  <%= render 'dashboard/progress_bar',
                      value: @vibe_metrics[:sentiment_distribution][:negative],
                      color: 'danger',
                      size: 'sm',
                      show_percentage: false %>
                  <span class="text-gray-900 font-semibold w-12 text-right"><%= @vibe_metrics[:sentiment_distribution][:negative] %>%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Campaigns -->
        <div class="bg-white rounded-xl shadow-sm">
          <div class="px-6 py-4">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Recent Campaigns</h3>
                <p class="text-sm text-gray-600 mt-1">Latest campaign activity</p>
              </div>
              <%= link_to campaigns_path, class: "inline-flex items-center space-x-1 text-blue-600 hover:text-blue-700 transition-colors text-sm font-medium" do %>
                <span>View All</span>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              <% end %>
            </div>
          </div>

          <div class="p-6">
            <% if @recent_campaigns.any? %>
              <div class="space-y-4">
                <% @recent_campaigns.first(4).each_with_index do |campaign, index| %>
                  <div class="group p-4 rounded-lg bg-gray-50 hover:bg-gray-100 hover:shadow-sm transition-all duration-200">
                    <div class="flex items-center justify-between">
                      <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                          <h4 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                            <%= campaign.name %>
                          </h4>
                          <%= render 'dashboard/status_badge', status: campaign.status, size: 'sm' %>
                        </div>
                        <div class="flex items-center space-x-6 text-sm text-gray-500">
                          <div class="flex items-center space-x-1">
                            <%= render 'shared/icons/heroicon', name: 'currency-dollar', class: 'w-4 h-4' %>
                            <span>$<%= number_with_delimiter(campaign.budget_in_dollars.round) %></span>
                          </div>
                          <div class="flex items-center space-x-1">
                            <%= render 'shared/icons/heroicon', name: 'chart-bar', class: 'w-4 h-4' %>
                            <span>Score: <%= rand(6.5..9.5).round(1) %>/10</span>
                          </div>
                          <div class="flex items-center space-x-1">
                            <%= render 'shared/icons/heroicon', name: 'calendar', class: 'w-4 h-4' %>
                            <span><%= campaign.updated_at.strftime('%b %d') %></span>
                          </div>
                        </div>
                      </div>

                      <div class="flex items-center space-x-2">
                        <%= link_to campaign_path(campaign), class: "p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200" do %>
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                          </svg>
                        <% end %>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <!-- Enhanced Empty State -->
              <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <%= render 'shared/icons/heroicon', name: 'chart-bar', class: 'w-8 h-8 text-gray-400' %>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No campaigns yet</h3>
                <p class="text-gray-600 mb-6">Create your first campaign to get started with AI-powered marketing</p>
                <%= link_to new_campaign_path, class: "inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl" do %>
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  <span>Create Campaign</span>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Right Sidebar -->
      <div class="space-y-8">

        <!-- Trending Vibes -->
        <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
          <div class="px-6 py-4 bg-gray-50">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Trending Vibes</h3>
                <p class="text-sm text-gray-600 mt-1">Popular emotional tones</p>
              </div>
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            </div>
          </div>

          <div class="p-6">
            <div class="space-y-4">
              <% @vibe_metrics[:trending_vibes].each_with_index do |vibe, index| %>
                <%
                  gradient_colors = [
                    'from-purple-500 to-pink-500',
                    'from-blue-500 to-cyan-500',
                    'from-green-500 to-emerald-500',
                    'from-orange-500 to-red-500',
                    'from-indigo-500 to-purple-500'
                  ]
                  current_gradient = gradient_colors[index % gradient_colors.length]
                %>
                <div class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-all duration-200">
                  <div class="flex items-center space-x-3">
                    <div class="flex items-center justify-center w-6 h-6 bg-blue-500 text-white text-xs font-semibold rounded-full">
                      <%= index + 1 %>
                    </div>
                    <span class="font-medium text-gray-900"><%= vibe %></span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">+<%= rand(5..25) %>%</span>
                    <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Emotional Breakdown -->
        <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
          <div class="px-6 py-4 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Emotional Breakdown</h3>
            <p class="text-sm text-gray-600 mt-1">Campaign emotional distribution</p>
          </div>

          <div class="p-6">
            <div class="space-y-4">
              <%
                emotion_colors = {
                  'joy' => 'warning',
                  'trust' => 'primary',
                  'anticipation' => 'success',
                  'surprise' => 'danger',
                  'other' => 'gray'
                }
              %>
              <% @emotional_resonance[:emotion_distribution].each_with_index do |(emotion, percentage), index| %>
                <div class="space-y-2 p-3 rounded-lg hover:bg-gray-50 transition-all duration-200">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <div class="w-3 h-3 rounded-full <%=
                        case emotion.to_s
                        when 'joy' then 'bg-yellow-500'
                        when 'trust' then 'bg-blue-500'
                        when 'anticipation' then 'bg-green-500'
                        when 'surprise' then 'bg-pink-500'
                        else 'bg-gray-500'
                        end
                      %>"></div>
                      <span class="font-medium text-gray-900 capitalize"><%= emotion %></span>
                    </div>
                    <span class="text-sm font-medium text-gray-700 bg-gray-100 px-2 py-1 rounded-full"><%= percentage %>%</span>
                  </div>
                  <%= render 'dashboard/progress_bar',
                      value: percentage,
                      color: emotion_colors[emotion.to_s] || 'primary',
                      size: 'sm',
                      show_percentage: false %>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Cultural Moments -->
        <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
          <div class="px-6 py-4 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Cultural Moments</h3>
            <p class="text-sm text-gray-600 mt-1">Trending cultural topics</p>
          </div>

          <div class="p-6">
            <div class="space-y-3">
              <%
                moment_gradients = [
                  'from-purple-500 to-pink-500',
                  'from-blue-500 to-cyan-500',
                  'from-green-500 to-emerald-500',
                  'from-orange-500 to-red-500',
                  'from-indigo-500 to-purple-500'
                ]
              %>
              <% @cultural_alignment[:trending_topics].each_with_index do |topic, index| %>
                <% current_gradient = moment_gradients[index % moment_gradients.length] %>
                <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-all duration-200">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span class="font-medium text-gray-900 flex-1"><%= topic %></span>
                  <span class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-full font-medium">Trending</span>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-gray-900 rounded-xl shadow-sm text-white overflow-hidden">
          <div class="p-6">
            <div class="text-center mb-6">
              <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <%= render 'shared/icons/heroicon', name: 'chart-bar', variant: 'solid', class: 'w-6 h-6 text-white' %>
              </div>
              <h3 class="text-lg font-semibold mb-2">Quick Actions</h3>
              <p class="text-gray-300 text-sm">Manage your campaigns and settings</p>
            </div>

            <div class="space-y-3">
              <%= link_to new_campaign_path, class: "w-full bg-blue-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 text-center block shadow-sm" do %>
                <div class="flex items-center justify-center space-x-2">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  <span>Create Campaign</span>
                </div>
              <% end %>

              <%= link_to campaigns_path, class: "w-full text-gray-300 px-4 py-3 rounded-lg font-medium hover:bg-gray-800 hover:text-white transition-all duration-200 text-center block" do %>
                <div class="flex items-center justify-center space-x-2">
                  <%= render 'shared/icons/heroicon', name: 'chart-bar', class: 'w-5 h-5' %>
                  <span>View All Campaigns</span>
                </div>
              <% end %>

              <%= link_to audiences_path, class: "w-full text-gray-300 px-4 py-3 rounded-lg font-medium hover:bg-gray-800 hover:text-white transition-all duration-200 text-center block" do %>
                <div class="flex items-center justify-center space-x-2">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                  <span>Manage Audiences</span>
                </div>
              <% end %>

              <%= link_to vibe_analytics_path, class: "w-full text-gray-300 px-4 py-3 rounded-lg font-medium hover:bg-gray-800 hover:text-white transition-all duration-200 text-center block" do %>
                <div class="flex items-center justify-center space-x-2">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    <circle cx="12" cy="12" r="2" fill="currentColor"/>
                  </svg>
                  <span>Vibe Analytics</span>
                </div>
              <% end %>

              <%= link_to "#", class: "w-full text-gray-300 px-4 py-3 rounded-lg font-medium hover:bg-gray-800 hover:text-white transition-all duration-200 text-center block" do %>
                <div class="flex items-center justify-center space-x-2">
                  <%= render 'shared/icons/heroicon', name: 'cog-6-tooth', class: 'w-5 h-5' %>
                  <span>Dashboard Settings</span>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Dashboard JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Enhanced metric animations with staggered loading
  const metrics = document.querySelectorAll('[data-dashboard-target="metric"]');
  metrics.forEach((metric, index) => {
    metric.style.opacity = '0';
    metric.style.transform = 'translateY(20px)';

    setTimeout(() => {
      metric.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
      metric.style.opacity = '1';
      metric.style.transform = 'translateY(0)';
    }, 100 + (index * 150)); // Staggered animation
  });

  // Animate progress bars
  const progressBars = document.querySelectorAll('[role="progressbar"]');
  progressBars.forEach((bar, index) => {
    const width = bar.style.width;
    bar.style.width = '0%';

    setTimeout(() => {
      bar.style.transition = 'width 1.2s ease-out';
      bar.style.width = width;
    }, 500 + (index * 100));
  });

  // Add hover effects to cards
  const cards = document.querySelectorAll('.bg-white.rounded-xl');
  cards.forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-2px)';
      this.style.boxShadow = '0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
    });

    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
      this.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)';
    });
  });

  // Auto-refresh functionality
  const refreshInterval = 30000; // 30 seconds
  let refreshTimer;

  function refreshDashboard() {
    // Add subtle loading indicator
    const refreshButton = document.querySelector('[title="Refresh Dashboard"]');
    if (refreshButton) {
      refreshButton.classList.add('animate-spin');

      // Simulate refresh (in real implementation, this would fetch new data)
      setTimeout(() => {
        refreshButton.classList.remove('animate-spin');

        // Show success indicator
        const originalHTML = refreshButton.innerHTML;
        refreshButton.innerHTML = '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';

        setTimeout(() => {
          refreshButton.innerHTML = originalHTML;
        }, 1000);
      }, 1000);
    }
  }

  // Set up auto-refresh
  function startAutoRefresh() {
    refreshTimer = setInterval(refreshDashboard, refreshInterval);
  }

  function stopAutoRefresh() {
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }
  }

  // Start auto-refresh
  startAutoRefresh();

  // Pause auto-refresh when page is not visible
  document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
      stopAutoRefresh();
    } else {
      startAutoRefresh();
    }
  });

  // Manual refresh button
  const refreshButton = document.querySelector('[title="Refresh Dashboard"]');
  if (refreshButton) {
    refreshButton.addEventListener('click', function(e) {
      e.preventDefault();
      refreshDashboard();
    });
  }
});
</script>